// pages/api/ai/human.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { google } from '@ai-sdk/google';
import { generateText, tool, convertToModelMessages, UIMessage } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';
import { query } from '@/lib/db';

let redis: any = null;

// Redis connection
async function getRedisClient() {
  if (!redis) {
    redis = createClient({
      socket: {
        host: 'mercury.nityasha.com',
        port: 26739,
      },
      password: 'Amber@!23',
    });

    redis.on('error', (err: unknown) => console.error('Redis error', err));
    await redis.connect();
  }
  return redis;
}

function historyKey(id: string) {
  return `chat:${id}:messages`;
}

// Normalize messages
function normalizeMessages(messages: any[]): UIMessage[] {
  return messages.map((m) => {
    // Handle different content formats
    let content;

    if (Array.isArray(m.content)) {
      // Already in array format, ensure each item has proper structure
      content = m.content.map((item: any) => {
        if (typeof item === 'string') {
          return { type: 'text', text: item };
        }
        if (item && typeof item === 'object' && item.text) {
          return { type: 'text', text: String(item.text) };
        }
        return { type: 'text', text: String(item || '') };
      });
    } else if (typeof m.content === 'string') {
      // Simple string content
      content = [{ type: 'text', text: m.content }];
    } else if (Array.isArray(m.parts)) {
      // Use parts if available
      content = m.parts.map((item: any) => {
        if (typeof item === 'string') {
          return { type: 'text', text: item };
        }
        if (item && typeof item === 'object' && item.text) {
          return { type: 'text', text: String(item.text) };
        }
        return { type: 'text', text: String(item || '') };
      });
    } else {
      // Fallback
      content = [{ type: 'text', text: String(m.content || m.parts || '') }];
    }

    return {
      ...m,
      content,
      parts: content,
    };
  });
}

async function loadHistory(id: string): Promise<UIMessage[]> {
  try {
    const client = await getRedisClient();
    const raw = await client.get(historyKey(id));
    const parsed = raw ? JSON.parse(raw) : [];
    return normalizeMessages(parsed);
  } catch (error) {
    console.error('Failed to load history:', error);
    return [];
  }
}

async function saveHistory(id: string, messages: UIMessage[]) {
  try {
    const client = await getRedisClient();
    const normalized = normalizeMessages(messages);
    await client.set(historyKey(id), JSON.stringify(normalized));
  } catch (error) {
    console.error('Failed to save history:', error);
  }
}

// Utility function to clear corrupted chat history
async function clearHistory(id: string) {
  try {
    const client = await getRedisClient();
    await client.del(historyKey(id));
    console.log(`Cleared history for chat: ${id}`);
  } catch (error) {
    console.error('Failed to clear history:', error);
  }
}

// Weather tool
const weatherTool = tool({
  description: 'Get current weather for a location',
  inputSchema: z.object({
    location: z.string().describe('City and region/country, e.g. "Guna, Madhya Pradesh, India"'),
    unit: z.enum(['celsius', 'fahrenheit']).optional(),
  }),
  execute: async ({ location, unit = 'celsius' }) => {
    const temperature = unit === 'fahrenheit' ? 86 : 30;
    return { location, unit, temperature, conditions: 'Sunny' };
  },
});

// Reminder tools
const addReminderTool = tool({
  description: 'Add a new reminder for the user',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    title: z.string().describe('Reminder title'),
    description: z.string().optional().describe('Reminder description'),
    reminder_date: z.string().describe('Reminder date and time in YYYY-MM-DD HH:MM:SS format'),
  }),
  execute: async ({ user_id, title, description, reminder_date }) => {
    try {
      const result = await query(
        'INSERT INTO reminders (user_id, title, description, reminder_date) VALUES (?, ?, ?, ?)',
        [user_id, title, description || '', reminder_date]
      ) as any;
      return { success: true, id: result.insertId, message: 'Reminder added successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to add reminder' };
    }
  },
});

const getRemindersTool = tool({
  description: 'Get all reminders for a user',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    completed: z.boolean().optional().describe('Filter by completion status'),
  }),
  execute: async ({ user_id, completed }) => {
    try {
      let sql = 'SELECT * FROM reminders WHERE user_id = ?';
      const params: any[] = [user_id];

      if (completed !== undefined) {
        sql += ' AND is_completed = ?';
        params.push(completed);
      }

      sql += ' ORDER BY reminder_date ASC';

      const reminders = await query(sql, params);
      return { success: true, reminders };
    } catch (error) {
      return { success: false, message: 'Failed to get reminders' };
    }
  },
});

const updateReminderTool = tool({
  description: 'Update a reminder',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    reminder_id: z.number().describe('Reminder ID to update'),
    title: z.string().optional().describe('New title'),
    description: z.string().optional().describe('New description'),
    reminder_date: z.string().optional().describe('New reminder date in YYYY-MM-DD HH:MM:SS format'),
    is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
  }),
  execute: async ({ user_id, reminder_id, title, description, reminder_date, is_completed }) => {
    try {
      const updates: string[] = [];
      const params: any[] = [];

      if (title) { updates.push('title = ?'); params.push(title); }
      if (description !== undefined) { updates.push('description = ?'); params.push(description); }
      if (reminder_date) { updates.push('reminder_date = ?'); params.push(reminder_date); }
      if (is_completed !== undefined) { updates.push('is_completed = ?'); params.push(is_completed); }

      if (updates.length === 0) {
        return { success: false, message: 'No fields to update' };
      }

      params.push(reminder_id, user_id);

      await query(
        `UPDATE reminders SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
        params
      );

      return { success: true, message: 'Reminder updated successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to update reminder' };
    }
  },
});

const deleteReminderTool = tool({
  description: 'Delete a reminder',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    reminder_id: z.number().describe('Reminder ID to delete'),
  }),
  execute: async ({ user_id, reminder_id }) => {
    try {
      await query('DELETE FROM reminders WHERE id = ? AND user_id = ?', [reminder_id, user_id]);
      return { success: true, message: 'Reminder deleted successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to delete reminder' };
    }
  },
});

// Todo List tools
const addTodoListTool = tool({
  description: 'Create a new todo list for the user',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    name: z.string().describe('Todo list name'),
    description: z.string().optional().describe('Todo list description'),
  }),
  execute: async ({ user_id, name, description }) => {
    try {
      const result = await query(
        'INSERT INTO todo_lists (user_id, name, description) VALUES (?, ?, ?)',
        [user_id, name, description || '']
      ) as any;
      return { success: true, id: result.insertId, message: 'Todo list created successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to create todo list' };
    }
  },
});

const getTodoListsTool = tool({
  description: 'Get all todo lists for a user',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
  }),
  execute: async ({ user_id }) => {
    try {
      const lists = await query('SELECT * FROM todo_lists WHERE user_id = ? ORDER BY created_at DESC', [user_id]);
      return { success: true, lists };
    } catch (error) {
      return { success: false, message: 'Failed to get todo lists' };
    }
  },
});

const addTodoItemTool = tool({
  description: 'Add a new todo item to a list',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    list_id: z.number().optional().describe('Todo list ID (optional, can be null for general items)'),
    title: z.string().describe('Todo item title'),
    description: z.string().optional().describe('Todo item description'),
    priority: z.enum(['low', 'medium', 'high']).optional().describe('Priority level'),
    due_date: z.string().optional().describe('Due date in YYYY-MM-DD HH:MM:SS format'),
  }),
  execute: async ({ user_id, list_id, title, description, priority, due_date }) => {
    try {
      const result = await query(
        'INSERT INTO todo_items (user_id, list_id, title, description, priority, due_date) VALUES (?, ?, ?, ?, ?, ?)',
        [user_id, list_id || null, title, description || '', priority || 'medium', due_date || null]
      ) as any;
      return { success: true, id: result.insertId, message: 'Todo item added successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to add todo item' };
    }
  },
});

const getTodoItemsTool = tool({
  description: 'Get todo items for a user, optionally filtered by list or completion status',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    list_id: z.number().optional().describe('Filter by specific todo list ID'),
    completed: z.boolean().optional().describe('Filter by completion status'),
  }),
  execute: async ({ user_id, list_id, completed }) => {
    try {
      let sql = `
        SELECT ti.*, tl.name as list_name
        FROM todo_items ti
        LEFT JOIN todo_lists tl ON ti.list_id = tl.id
        WHERE ti.user_id = ?
      `;
      const params: any[] = [user_id];

      if (list_id !== undefined) {
        sql += ' AND ti.list_id = ?';
        params.push(list_id);
      }

      if (completed !== undefined) {
        sql += ' AND ti.is_completed = ?';
        params.push(completed);
      }

      sql += ' ORDER BY ti.due_date ASC, ti.priority DESC, ti.created_at DESC';

      const items = await query(sql, params);
      return { success: true, items };
    } catch (error) {
      return { success: false, message: 'Failed to get todo items' };
    }
  },
});

const updateTodoItemTool = tool({
  description: 'Update a todo item',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    item_id: z.number().describe('Todo item ID to update'),
    title: z.string().optional().describe('New title'),
    description: z.string().optional().describe('New description'),
    priority: z.enum(['low', 'medium', 'high']).optional().describe('New priority'),
    due_date: z.string().optional().describe('New due date in YYYY-MM-DD HH:MM:SS format'),
    is_completed: z.boolean().optional().describe('Mark as completed/incomplete'),
  }),
  execute: async ({ user_id, item_id, title, description, priority, due_date, is_completed }) => {
    try {
      const updates: string[] = [];
      const params: any[] = [];

      if (title) { updates.push('title = ?'); params.push(title); }
      if (description !== undefined) { updates.push('description = ?'); params.push(description); }
      if (priority) { updates.push('priority = ?'); params.push(priority); }
      if (due_date !== undefined) { updates.push('due_date = ?'); params.push(due_date || null); }
      if (is_completed !== undefined) { updates.push('is_completed = ?'); params.push(is_completed); }

      if (updates.length === 0) {
        return { success: false, message: 'No fields to update' };
      }

      params.push(item_id, user_id);

      await query(
        `UPDATE todo_items SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
        params
      );

      return { success: true, message: 'Todo item updated successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to update todo item' };
    }
  },
});

const deleteTodoItemTool = tool({
  description: 'Delete a todo item',
  inputSchema: z.object({
    user_id: z.number().describe('User ID'),
    item_id: z.number().describe('Todo item ID to delete'),
  }),
  execute: async ({ user_id, item_id }) => {
    try {
      await query('DELETE FROM todo_items WHERE id = ? AND user_id = ?', [item_id, user_id]);
      return { success: true, message: 'Todo item deleted successfully' };
    } catch (error) {
      return { success: false, message: 'Failed to delete todo item' };
    }
  },
});

    

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const { user_id, message }: { user_id: number; message: string } = req.body;

    if (!user_id || !message) {
      return res.status(400).json({ error: 'Missing required fields: user_id and message' });
    }

    // Convert simple format to internal format
    const id = `chat_${user_id}`;
    const messages: any[] = [
      {
        id: crypto.randomUUID(),
        role: 'user',
        content: [{ type: 'text', text: message }],
        parts: [{ type: 'text', text: message }],
      }
    ];

    const prior = await loadHistory(id);

    // Check for corrupted data and clear if needed
    const hasCorruptedData = prior.some((msg: any) =>
      msg.role === 'assistant' &&
      msg.content &&
      Array.isArray(msg.content) &&
      msg.content.some((c: any) => c.text === '[object Object]')
    );

    if (hasCorruptedData) {
      console.log('Detected corrupted data, clearing history for chat:', id);
      await clearHistory(id);
      // Reload after clearing
      const cleanPrior = await loadHistory(id);
      var fullUI = normalizeMessages([...cleanPrior, ...messages]);
    } else {
      // normalize both prior + new messages
      var fullUI = normalizeMessages([...prior, ...messages]);
    }

    console.log('fullUI messages:', JSON.stringify(fullUI, null, 2));

    // Additional debug: check what convertToModelMessages produces
    const modelMessages = convertToModelMessages(fullUI);
    console.log('Model messages:', JSON.stringify(modelMessages, null, 2));

    // get final response (non-streaming)
    const result = await generateText({
      model: google('gemini-2.5-flash'),
      messages: modelMessages,
      tools: { get_current_weather: weatherTool },
    });

    // Debug: check what result contains
    console.log('Tool calls:', result.toolCalls);
    console.log('Tool results:', result.toolResults);

    // Handle the response properly
    let aiResponse = result.text;

    // If no text but there are tool results, create a response
    if (!aiResponse && result.toolResults && result.toolResults.length > 0) {
      const toolResult = result.toolResults[0];
      if (toolResult.toolName === 'get_current_weather') {
        const weather = (toolResult as any).output;
        aiResponse = `The current weather in ${weather.location} is ${weather.temperature}°${weather.unit} with ${weather.conditions} conditions.`;
      }
    }

    // Fallback if still no response
    if (!aiResponse) {
      aiResponse = 'I apologize, but I was unable to generate a response. Please try again.';
    }

    // persist assistant response
    const assistantMessage = {
      id: crypto.randomUUID(),
      role: 'assistant' as const,
      content: [{ type: 'text' as const, text: aiResponse }],
      parts: [{ type: 'text' as const, text: aiResponse }],
    };

    const updated = [...fullUI, assistantMessage];
    await saveHistory(id, updated);

    // ✅ return in JSON format instead of plain text
    return res.status(200).json({ response: aiResponse });
  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
