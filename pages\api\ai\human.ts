// pages/api/ai/human.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { google } from '@ai-sdk/google';
import { generateText, tool, convertToModelMessages, UIMessage } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';

let redis: any = null;

// Redis connection
async function getRedisClient() {
  if (!redis) {
    redis = createClient({
      socket: {
        host: 'mercury.nityasha.com',
        port: 26739,
      },
      password: 'Amber@!23',
    });

    redis.on('error', (err: unknown) => console.error('Redis error', err));
    await redis.connect();
  }
  return redis;
}

function historyKey(id: string) {
  return `chat:${id}:messages`;
}

// Normalize messages
function normalizeMessages(messages: any[]): UIMessage[] {
  return messages.map((m) => {
    const content = Array.isArray(m.content)
      ? m.content
      : Array.isArray(m.parts)
      ? m.parts
      : [{ type: 'text', text: String(m.content || m.parts || '') }];

    return {
      ...m,
      content,
      parts: content,
    };
  });
}

async function loadHistory(id: string): Promise<UIMessage[]> {
  try {
    const client = await getRedisClient();
    const raw = await client.get(historyKey(id));
    const parsed = raw ? JSON.parse(raw) : [];
    return normalizeMessages(parsed);
  } catch (error) {
    console.error('Failed to load history:', error);
    return [];
  }
}

async function saveHistory(id: string, messages: UIMessage[]) {
  try {
    const client = await getRedisClient();
    const normalized = normalizeMessages(messages);
    await client.set(historyKey(id), JSON.stringify(normalized));
  } catch (error) {
    console.error('Failed to save history:', error);
  }
}

// Example tool
const weatherTool = tool({
  description: 'Get current weather for a location',
  inputSchema: z.object({
    location: z.string().describe('City and region/country, e.g. "Guna, Madhya Pradesh, India"'),
    unit: z.enum(['celsius', 'fahrenheit']).optional(),
  }),
  execute: async ({ location, unit = 'celsius' }) => {
    const temperature = unit === 'fahrenheit' ? 86 : 30;
    return { location, unit, temperature, conditions: 'Sunny' };
  },
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const { id, messages }: { id: string; messages: UIMessage[] } = req.body;

    if (!id || !messages) {
      return res.status(400).json({ error: 'Missing required fields: id and messages' });
    }

    const prior = await loadHistory(id);

    // normalize both prior + new messages
    const fullUI = normalizeMessages([...prior, ...messages]);

    console.log('fullUI messages:', JSON.stringify(fullUI, null, 2));

    // get final response (non-streaming)
    const result = await generateText({
      model: google('gemini-2.5-flash'),
      messages: convertToModelMessages(fullUI),
      tools: { get_current_weather: weatherTool },
    });

    const aiResponse = result.text;

    // persist assistant response
    const assistantMessage = {
      id: crypto.randomUUID(),
      role: 'assistant' as const,
      content: [{ type: 'text' as const, text: aiResponse }],
      parts: [{ type: 'text' as const, text: aiResponse }],
    };

    const updated = [...fullUI, assistantMessage];
    await saveHistory(id, updated);

    // ✅ return in JSON format instead of plain text
    return res.status(200).json({ response: aiResponse });
  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
