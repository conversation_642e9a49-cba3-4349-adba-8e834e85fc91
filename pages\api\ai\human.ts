// pages/api/ai/human.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { google } from '@ai-sdk/google';
import { generateText, tool, convertToModelMessages, UIMessage } from 'ai';
import { z } from 'zod';
import { createClient } from 'redis';

let redis: any = null;

// Redis connection
async function getRedisClient() {
  if (!redis) {
    redis = createClient({
      socket: {
        host: 'mercury.nityasha.com',
        port: 26739,
      },
      password: 'Amber@!23',
    });

    redis.on('error', (err: unknown) => console.error('Redis error', err));
    await redis.connect();
  }
  return redis;
}

function historyKey(id: string) {
  return `chat:${id}:messages`;
}

// Normalize messages
function normalizeMessages(messages: any[]): UIMessage[] {
  return messages.map((m) => {
    // Handle different content formats
    let content;

    if (Array.isArray(m.content)) {
      // Already in array format, ensure each item has proper structure
      content = m.content.map((item: any) => {
        if (typeof item === 'string') {
          return { type: 'text', text: item };
        }
        if (item && typeof item === 'object' && item.text) {
          return { type: 'text', text: String(item.text) };
        }
        return { type: 'text', text: String(item || '') };
      });
    } else if (typeof m.content === 'string') {
      // Simple string content
      content = [{ type: 'text', text: m.content }];
    } else if (Array.isArray(m.parts)) {
      // Use parts if available
      content = m.parts.map((item: any) => {
        if (typeof item === 'string') {
          return { type: 'text', text: item };
        }
        if (item && typeof item === 'object' && item.text) {
          return { type: 'text', text: String(item.text) };
        }
        return { type: 'text', text: String(item || '') };
      });
    } else {
      // Fallback
      content = [{ type: 'text', text: String(m.content || m.parts || '') }];
    }

    return {
      ...m,
      content,
      parts: content,
    };
  });
}

async function loadHistory(id: string): Promise<UIMessage[]> {
  try {
    const client = await getRedisClient();
    const raw = await client.get(historyKey(id));
    const parsed = raw ? JSON.parse(raw) : [];
    return normalizeMessages(parsed);
  } catch (error) {
    console.error('Failed to load history:', error);
    return [];
  }
}

async function saveHistory(id: string, messages: UIMessage[]) {
  try {
    const client = await getRedisClient();
    const normalized = normalizeMessages(messages);
    await client.set(historyKey(id), JSON.stringify(normalized));
  } catch (error) {
    console.error('Failed to save history:', error);
  }
}

// Utility function to clear corrupted chat history
async function clearHistory(id: string) {
  try {
    const client = await getRedisClient();
    await client.del(historyKey(id));
    console.log(`Cleared history for chat: ${id}`);
  } catch (error) {
    console.error('Failed to clear history:', error);
  }
}

// Example tool
const weatherTool = tool({
  description: 'Get current weather for a location',
  inputSchema: z.object({
    location: z.string().describe('City and region/country, e.g. "Guna, Madhya Pradesh, India"'),
    unit: z.enum(['celsius', 'fahrenheit']).optional(),
  }),
  execute: async ({ location, unit = 'celsius' }) => {
    const temperature = unit === 'fahrenheit' ? 86 : 30;
    return { location, unit, temperature, conditions: 'Sunny' };
  },
});

const weatherTool = tool({
  description: 'Get current weather for a location',
  inputSchema: z.object({
    location: z.string().describe('City and region/country, e.g. "Guna, Madhya Pradesh, India"'),
    unit: z.enum(['celsius', 'fahrenheit']).optional(),
  }),
  execute: async ({ location, unit = 'celsius' }) => {
    const temperature = unit === 'fahrenheit' ? 86 : 30;
    return { location, unit, temperature, conditions: 'Sunny' };
  },
});


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    const { id, messages }: { id: string; messages: UIMessage[] } = req.body;

    if (!id || !messages) {
      return res.status(400).json({ error: 'Missing required fields: id and messages' });
    }

    const prior = await loadHistory(id);

    // Check for corrupted data and clear if needed
    const hasCorruptedData = prior.some((msg: any) =>
      msg.role === 'assistant' &&
      msg.content &&
      Array.isArray(msg.content) &&
      msg.content.some((c: any) => c.text === '[object Object]')
    );

    if (hasCorruptedData) {
      console.log('Detected corrupted data, clearing history for chat:', id);
      await clearHistory(id);
      // Reload after clearing
      const cleanPrior = await loadHistory(id);
      var fullUI = normalizeMessages([...cleanPrior, ...messages]);
    } else {
      // normalize both prior + new messages
      var fullUI = normalizeMessages([...prior, ...messages]);
    }

    console.log('fullUI messages:', JSON.stringify(fullUI, null, 2));

    // Additional debug: check what convertToModelMessages produces
    const modelMessages = convertToModelMessages(fullUI);
    console.log('Model messages:', JSON.stringify(modelMessages, null, 2));

    // get final response (non-streaming)
    const result = await generateText({
      model: google('gemini-2.5-flash'),
      messages: modelMessages,
      tools: { get_current_weather: weatherTool },
    });

    // Debug: check what result contains
    console.log('Tool calls:', result.toolCalls);
    console.log('Tool results:', result.toolResults);

    // Handle the response properly
    let aiResponse = result.text;

    // If no text but there are tool results, create a response
    if (!aiResponse && result.toolResults && result.toolResults.length > 0) {
      const toolResult = result.toolResults[0];
      if (toolResult.toolName === 'get_current_weather') {
        const weather = (toolResult as any).output;
        aiResponse = `The current weather in ${weather.location} is ${weather.temperature}°${weather.unit} with ${weather.conditions} conditions.`;
      }
    }

    // Fallback if still no response
    if (!aiResponse) {
      aiResponse = 'I apologize, but I was unable to generate a response. Please try again.';
    }

    // persist assistant response
    const assistantMessage = {
      id: crypto.randomUUID(),
      role: 'assistant' as const,
      content: [{ type: 'text' as const, text: aiResponse }],
      parts: [{ type: 'text' as const, text: aiResponse }],
    };

    const updated = [...fullUI, assistantMessage];
    await saveHistory(id, updated);

    // ✅ return in JSON format instead of plain text
    return res.status(200).json({ response: aiResponse });
  } catch (error) {
    console.error('API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
